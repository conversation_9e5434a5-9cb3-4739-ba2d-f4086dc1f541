#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Advanced Sound Synthesis Engine
==============================================

Pokročilý systém syntézy zvuku implementující moderní algoritmy
pro vytváření realistických a expresivních zvukových efektů.

Implementované techniky:
- Karplus-Strong algoritmus (physical modeling strun)
- Digital Waveguide syntéza (dechové nástroje)
- FM syntéza (elektronické zvuky)
- Granular syntéza (textury a atmosféra)
- Physical modeling (realistick<PERSON> zvuky)

Autor: AI Assistant
Verze: 3.0
"""

import numpy as np
import scipy.signal as signal
import scipy.io.wavfile as wavfile
import os
import random
import math
from typing import Tuple, List, Optional
import threading
import time

class AdvancedSoundSynthesis:
    """Pokročilý engine pro syntézu zvuku"""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
        self.output_dir = "game_assets/sounds/advanced"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🎼 Advanced Sound Synthesis Engine inicializován")
        print(f"📁 Výstupní složka: {self.output_dir}")
        print("🔬 Implementované techniky:")
        print("   • Karplus-Strong algoritmus")
        print("   • Digital Waveguide syntéza")
        print("   • FM syntéza")
        print("   • Granular syntéza")
        print("   • Physical modeling")
    
    def karplus_strong_string(self, frequency: float, duration: float, 
                             pluck_position: float = 0.5, 
                             damping: float = 0.996) -> np.ndarray:
        """
        Implementace Karplus-Strong algoritmu pro syntézu strun
        
        Args:
            frequency: Základní frekvence
            pluck_position: Pozice škubnutí (0.0-1.0)
            damping: Tlumení (0.0-1.0)
        """
        # Délka delay line
        delay_length = int(self.sample_rate / frequency)
        
        # Inicializace delay line s náhodným šumem
        delay_line = np.random.uniform(-1, 1, delay_length)
        
        # Simulace pozice škubnutí
        if pluck_position != 0.5:
            # Vytvoření dvou delay lines pro simulaci pozice škubnutí
            split_point = int(delay_length * pluck_position)
            delay_line[:split_point] *= -1
        
        # Generování výstupu
        samples = int(duration * self.sample_rate)
        output = np.zeros(samples)
        
        for i in range(samples):
            # Výstup je aktuální vzorek
            output[i] = delay_line[0]
            
            # Karplus-Strong filtr (průměr dvou vzorků s tlumením)
            new_sample = damping * 0.5 * (delay_line[0] + delay_line[1])
            
            # Posun delay line
            delay_line[:-1] = delay_line[1:]
            delay_line[-1] = new_sample
        
        return output * 0.3  # Normalizace
    
    def digital_waveguide_flute(self, frequency: float, duration: float,
                               breath_pressure: float = 0.7,
                               embouchure: float = 0.5) -> np.ndarray:
        """
        Digital Waveguide model flétny
        
        Args:
            frequency: Základní frekvence
            breath_pressure: Síla dechu (0.0-1.0)
            embouchure: Nastavení rtů (0.0-1.0)
        """
        # Délka delay line
        delay_length = int(self.sample_rate / frequency / 2)  # Polovina pro open tube
        
        # Inicializace delay lines (forward a backward)
        forward_line = np.zeros(delay_length)
        backward_line = np.zeros(delay_length)
        
        # Parametry
        samples = int(duration * self.sample_rate)
        output = np.zeros(samples)
        
        # Filtr pro simulaci ztráty energie
        cutoff = frequency * 2 * embouchure + 1000
        b, a = signal.butter(2, cutoff / (self.sample_rate / 2), 'low')
        filter_state = signal.lfilter_zi(b, a)
        
        for i in range(samples):
            # Excitation (simulace dechu)
            if i < samples * 0.1:  # Attack
                excitation = breath_pressure * np.random.normal(0, 0.1) * (i / (samples * 0.1))
            else:
                excitation = breath_pressure * np.random.normal(0, 0.05)
            
            # Waveguide junction
            junction_input = forward_line[0] + backward_line[0] + excitation
            
            # Reflection coefficients
            r1 = -0.95  # Closed end
            r2 = 0.95   # Open end
            
            # Nové hodnoty pro delay lines
            new_forward = r2 * backward_line[-1]
            new_backward = r1 * forward_line[-1] + junction_input
            
            # Aplikace filtru na backward line
            new_backward, filter_state = signal.lfilter(b, a, [new_backward], zi=filter_state)
            new_backward = new_backward[0]
            
            # Výstup
            output[i] = (forward_line[0] + backward_line[0]) * 0.5
            
            # Posun delay lines
            forward_line[1:] = forward_line[:-1]
            forward_line[0] = new_forward
            
            backward_line[1:] = backward_line[:-1]
            backward_line[0] = new_backward
        
        return output * 0.4
    
    def fm_synthesis(self, carrier_freq: float, modulator_freq: float,
                    modulation_index: float, duration: float,
                    envelope_type: str = "adsr") -> np.ndarray:
        """
        FM syntéza pro elektronické zvuky
        
        Args:
            carrier_freq: Frekvence nosné vlny
            modulator_freq: Frekvence modulátoru
            modulation_index: Index modulace
            envelope_type: Typ obálky
        """
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Modulátor
        modulator = np.sin(2 * np.pi * modulator_freq * t)
        
        # FM syntéza
        fm_signal = np.sin(2 * np.pi * carrier_freq * t + 
                          modulation_index * modulator)
        
        # Envelope
        if envelope_type == "adsr":
            envelope = self._create_adsr_envelope(samples, 0.1, 0.2, 0.6, 0.3)
        else:
            envelope = np.ones(samples)
        
        return fm_signal * envelope * 0.3
    
    def granular_synthesis(self, source_freq: float, grain_size: float,
                          grain_density: float, duration: float,
                          pitch_variation: float = 0.1) -> np.ndarray:
        """
        Granular syntéza pro textury
        
        Args:
            source_freq: Základní frekvence
            grain_size: Velikost zrna v sekundách
            grain_density: Hustota zrn (zrna/sekunda)
            pitch_variation: Variace výšky tónu
        """
        samples = int(duration * self.sample_rate)
        output = np.zeros(samples)
        
        grain_samples = int(grain_size * self.sample_rate)
        grains_per_second = grain_density
        
        # Generování zrn
        num_grains = int(duration * grains_per_second)
        
        for grain_idx in range(num_grains):
            # Náhodná pozice zrna
            start_pos = random.uniform(0, duration - grain_size)
            start_sample = int(start_pos * self.sample_rate)
            
            # Náhodná variace výšky
            freq_variation = random.uniform(-pitch_variation, pitch_variation)
            grain_freq = source_freq * (1 + freq_variation)
            
            # Generování zrna
            grain_t = np.linspace(0, grain_size, grain_samples, False)
            grain = np.sin(2 * np.pi * grain_freq * grain_t)
            
            # Hanning window pro zrno
            window = np.hanning(grain_samples)
            grain *= window
            
            # Přidání zrna do výstupu
            end_sample = start_sample + grain_samples
            if end_sample <= samples:
                output[start_sample:end_sample] += grain * 0.1
        
        return output
    
    def physical_drum(self, frequency: float, duration: float,
                     membrane_tension: float = 0.8,
                     damping: float = 0.995) -> np.ndarray:
        """
        Physical modeling bicích pomocí 2D wave equation
        """
        # Zjednodušený model - kombinace několika módů
        samples = int(duration * self.sample_rate)
        output = np.zeros(samples)
        
        # Různé módy membrány
        modes = [
            (frequency, 1.0),           # Základní mód
            (frequency * 1.59, 0.6),   # První overtone
            (frequency * 2.14, 0.4),   # Druhý overtone
            (frequency * 2.65, 0.3),   # Třetí overtone
        ]
        
        for mode_freq, amplitude in modes:
            # Karplus-Strong pro každý mód
            mode_signal = self.karplus_strong_string(
                mode_freq, duration, 
                pluck_position=0.3,  # Pozice úderu
                damping=damping * membrane_tension
            )
            output += mode_signal * amplitude
        
        # Exponenciální útlum
        decay = np.exp(-np.linspace(0, 5, samples))
        output *= decay
        
        return output * 0.5
    
    def _create_adsr_envelope(self, samples: int, attack: float, 
                             decay: float, sustain: float, 
                             release: float) -> np.ndarray:
        """Vytvoří ADSR envelope"""
        envelope = np.ones(samples)
        duration = samples / self.sample_rate
        
        attack_samples = int(attack * duration * self.sample_rate)
        decay_samples = int(decay * duration * self.sample_rate)
        release_samples = int(release * duration * self.sample_rate)
        sustain_samples = samples - attack_samples - decay_samples - release_samples
        
        if attack_samples > 0:
            envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
        
        if decay_samples > 0:
            start_idx = attack_samples
            end_idx = start_idx + decay_samples
            envelope[start_idx:end_idx] = np.linspace(1, sustain, decay_samples)
        
        if sustain_samples > 0:
            start_idx = attack_samples + decay_samples
            end_idx = start_idx + sustain_samples
            envelope[start_idx:end_idx] = sustain
        
        if release_samples > 0:
            envelope[-release_samples:] = np.linspace(sustain, 0, release_samples)
        
        return envelope
    
    def save_audio(self, audio_data: np.ndarray, filename: str) -> None:
        """Uloží audio data do WAV souboru"""
        # Normalizace
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            audio_data = audio_data / max_val
        
        # Stereo
        stereo_data = np.column_stack((audio_data, audio_data))
        
        # Konverze na 16-bit
        audio_int = (stereo_data * 32767).astype(np.int16)
        
        filepath = os.path.join(self.output_dir, filename)
        wavfile.write(filepath, self.sample_rate, audio_int)
        print(f"🎵 Uloženo: {filename}")

class GameSoundGenerator:
    """Generátor zvuků specifických pro hru Light or Dead"""
    
    def __init__(self, synthesis_engine: AdvancedSoundSynthesis):
        self.synth = synthesis_engine
    
    def generate_weapon_sounds(self):
        """Generuje pokročilé zvuky zbraní"""
        print("\n🔫 Generuji pokročilé zvuky zbraní...")
        
        # Laser - FM syntéza
        laser = self.synth.fm_synthesis(
            carrier_freq=800, 
            modulator_freq=200, 
            modulation_index=5, 
            duration=0.3
        )
        self.synth.save_audio(laser, "advanced_laser.wav")
        
        # Plasma rifle - Granular syntéza
        plasma = self.synth.granular_synthesis(
            source_freq=400,
            grain_size=0.01,
            grain_density=200,
            duration=0.4,
            pitch_variation=0.3
        )
        self.synth.save_audio(plasma, "plasma_rifle.wav")
        
        # Railgun - Kombinace FM a physical modeling
        railgun_fm = self.synth.fm_synthesis(1200, 50, 8, 0.6)
        railgun_physical = self.synth.karplus_strong_string(150, 0.6, 0.2, 0.99)
        railgun = railgun_fm + railgun_physical * 0.5
        self.synth.save_audio(railgun, "railgun.wav")
    
    def generate_ambient_sounds(self):
        """Generuje atmosférické zvuky"""
        print("\n🌙 Generuji atmosférické zvuky...")
        
        # Temná atmosféra - Granular syntéza
        dark_ambient = self.synth.granular_synthesis(
            source_freq=60,
            grain_size=0.2,
            grain_density=10,
            duration=10.0,
            pitch_variation=0.5
        )
        self.synth.save_audio(dark_ambient, "dark_atmosphere.wav")
        
        # Větrný zvuk - Digital Waveguide
        wind = self.synth.digital_waveguide_flute(
            frequency=80,
            duration=8.0,
            breath_pressure=0.3,
            embouchure=0.2
        )
        self.synth.save_audio(wind, "wind_ambient.wav")
    
    def generate_monster_sounds(self):
        """Generuje zvuky monster"""
        print("\n👹 Generuji zvuky monster...")
        
        # Growl - Kombinace FM a physical modeling
        growl_fm = self.synth.fm_synthesis(120, 30, 3, 2.0)
        growl_string = self.synth.karplus_strong_string(80, 2.0, 0.1, 0.98)
        growl = growl_fm * 0.7 + growl_string * 0.3
        self.synth.save_audio(growl, "monster_growl.wav")
        
        # Roar - Physical modeling
        roar = self.synth.physical_drum(
            frequency=100,
            duration=1.5,
            membrane_tension=0.6,
            damping=0.992
        )
        self.synth.save_audio(roar, "monster_roar.wav")
    
    def generate_impact_sounds(self):
        """Generuje zvuky nárazů a explozí"""
        print("\n💥 Generuji zvuky nárazů...")
        
        # Výbuch - Physical modeling bicích
        explosion = self.synth.physical_drum(
            frequency=60,
            duration=2.0,
            membrane_tension=0.9,
            damping=0.985
        )
        self.synth.save_audio(explosion, "advanced_explosion.wav")
        
        # Metalický náraz - Karplus-Strong
        metal_impact = self.synth.karplus_strong_string(
            frequency=200,
            duration=1.0,
            pluck_position=0.1,
            damping=0.999
        )
        self.synth.save_audio(metal_impact, "metal_impact.wav")

def main():
    """Hlavní funkce"""
    print("🚀 Light or Dead - Advanced Sound Synthesis")
    print("=" * 60)
    
    # Inicializace synthesis engine
    synth_engine = AdvancedSoundSynthesis()
    
    # Inicializace game sound generátoru
    game_sounds = GameSoundGenerator(synth_engine)
    
    # Generování všech zvuků
    game_sounds.generate_weapon_sounds()
    game_sounds.generate_ambient_sounds()
    game_sounds.generate_monster_sounds()
    game_sounds.generate_impact_sounds()
    
    print("\n✅ POKROČILÉ ZVUKY VYGENEROVÁNY!")
    print(f"📁 Soubory uloženy do: {synth_engine.output_dir}")
    print("\n🎮 Nyní máte k dispozici realistické zvuky vytvořené")
    print("   pomocí pokročilých algoritmů syntézy!")
    print("\n💡 Použité techniky:")
    print("   • Karplus-Strong - pro struny a kovy")
    print("   • Digital Waveguide - pro dechové nástroje a vítr")
    print("   • FM syntéza - pro elektronické zvuky")
    print("   • Granular syntéza - pro textury a atmosféru")
    print("   • Physical modeling - pro realistické bicí")

if __name__ == "__main__":
    main()

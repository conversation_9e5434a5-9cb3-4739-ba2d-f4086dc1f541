#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Apply Quality Sounds
===================================

Rychle aplikuje stažené kvalitní zvuky do hry Light or Dead.

Autor: AI Assistant
Verze: 1.0
"""

import os
import shutil

def apply_quality_sounds():
    """Aplikuje kvalitní zvuky do hry"""
    
    print("🎵 Aplikuji kvalitní zvuky do Light or Dead")
    print("=" * 50)
    
    # Cesty
    quality_dir = "game_assets/sounds/quality"
    game_sounds_dir = "game_assets/sounds"
    
    # Mapování stažených zvuků na herní názvy
    sound_mapping = {
        # Mixkit zvuky → herní názvy
        "mixkit_gun_shot.wav": "shoot.wav",
        "mixkit_explosion.wav": "explosion.wav",
        "mixkit_hit.wav": "hit.wav",
        "mixkit_button_click.wav": "button_click.wav",
        "mixkit_pickup.wav": "pickup.wav",
        "mixkit_powerup.wav": "powerup.wav",
        "mixkit_error.wav": "shop_error.wav",
        "mixkit_player_hurt.wav": "player_hurt.wav",
        "mixkit_game_over.wav": "game_over.wav",
    }
    
    # Kontrola existence složek
    if not os.path.exists(quality_dir):
        print(f"❌ Složka {quality_dir} neexistuje!")
        print("💡 Nejdříve spusťte: python download_quality_sounds.py")
        return False
    
    if not os.path.exists(game_sounds_dir):
        os.makedirs(game_sounds_dir)
    
    # Vytvoření zálohy
    backup_dir = "game_assets/sounds/backup_synthetic"
    if not os.path.exists(backup_dir):
        os.makedirs(backup_dir)
        print(f"📁 Vytvořena záloha: {backup_dir}")
        
        # Zálohování syntetických zvuků
        for mixkit_file, game_file in sound_mapping.items():
            game_path = os.path.join(game_sounds_dir, game_file)
            if os.path.exists(game_path):
                backup_path = os.path.join(backup_dir, game_file)
                shutil.copy2(game_path, backup_path)
                print(f"💾 Zálohováno: {game_file}")
    
    # Aplikování kvalitních zvuků
    print("\n🔄 Aplikuji kvalitní zvuky...")
    applied_count = 0
    
    for mixkit_file, game_file in sound_mapping.items():
        quality_path = os.path.join(quality_dir, mixkit_file)
        game_path = os.path.join(game_sounds_dir, game_file)
        
        if os.path.exists(quality_path):
            shutil.copy2(quality_path, game_path)
            print(f"✅ {mixkit_file} → {game_file}")
            applied_count += 1
        else:
            print(f"⚠️ Nenalezen: {mixkit_file}")
    
    # Vytvoření chybějících zvuků (duplikáty pro různé zbraně)
    print("\n🔄 Vytvářím varianty zvuků...")
    
    # Shotgun a machinegun použijí stejný zvuk jako pistol (prozatím)
    base_shoot = os.path.join(game_sounds_dir, "shoot.wav")
    if os.path.exists(base_shoot):
        shutil.copy2(base_shoot, os.path.join(game_sounds_dir, "shotgun_shoot.wav"))
        shutil.copy2(base_shoot, os.path.join(game_sounds_dir, "machinegun_shoot.wav"))
        print("✅ Vytvořeny varianty zbraní")
    
    # Enemy death použije hit zvuk
    base_hit = os.path.join(game_sounds_dir, "hit.wav")
    if os.path.exists(base_hit):
        shutil.copy2(base_hit, os.path.join(game_sounds_dir, "enemy_death.wav"))
        print("✅ Vytvořen enemy_death.wav")
    
    # Boss zvuky použijí explosion
    base_explosion = os.path.join(game_sounds_dir, "explosion.wav")
    if os.path.exists(base_explosion):
        shutil.copy2(base_explosion, os.path.join(game_sounds_dir, "boss_spawn.wav"))
        shutil.copy2(base_explosion, os.path.join(game_sounds_dir, "boss_death.wav"))
        print("✅ Vytvořeny boss zvuky")
    
    # Shop buy použije pickup
    base_pickup = os.path.join(game_sounds_dir, "pickup.wav")
    if os.path.exists(base_pickup):
        shutil.copy2(base_pickup, os.path.join(game_sounds_dir, "shop_buy.wav"))
        print("✅ Vytvořen shop_buy.wav")
    
    # Wave start použije powerup
    base_powerup = os.path.join(game_sounds_dir, "powerup.wav")
    if os.path.exists(base_powerup):
        shutil.copy2(base_powerup, os.path.join(game_sounds_dir, "wave_start.wav"))
        print("✅ Vytvořen wave_start.wav")
    
    # Trap trigger použije button click
    base_click = os.path.join(game_sounds_dir, "button_click.wav")
    if os.path.exists(base_click):
        shutil.copy2(base_click, os.path.join(game_sounds_dir, "trap_trigger.wav"))
        print("✅ Vytvořen trap_trigger.wav")
    
    print(f"\n✅ HOTOVO! Aplikováno {applied_count} kvalitních zvuků")
    print("🎮 Kvalitní zvuky jsou nyní aktivní ve hře!")
    print(f"💾 Syntetické zvuky zálohované v: {backup_dir}")
    
    return True

def list_quality_sounds():
    """Vypíše dostupné kvalitní zvuky"""
    
    quality_dir = "game_assets/sounds/quality"
    
    print("🎵 Dostupné kvalitní zvuky:")
    print("=" * 40)
    
    if os.path.exists(quality_dir):
        files = [f for f in os.listdir(quality_dir) if f.endswith(('.wav', '.mp3'))]
        if files:
            for file in sorted(files):
                file_path = os.path.join(quality_dir, file)
                size = os.path.getsize(file_path)
                print(f"🎵 {file} ({size} bytes)")
        else:
            print("📭 Žádné zvuky nenalezeny")
    else:
        print("📁 Složka neexistuje")
    
    print(f"\n📁 Celkem souborů: {len(files) if 'files' in locals() else 0}")

def main():
    """Hlavní funkce"""
    
    print("🎮 Light or Dead - Quality Sound Manager")
    print("=" * 50)
    print("1. Aplikovat kvalitní zvuky")
    print("2. Zobrazit dostupné zvuky")
    print("3. Ukončit")
    
    choice = input("\nVyberte možnost (1-3): ").strip()
    
    if choice == "1":
        apply_quality_sounds()
    elif choice == "2":
        list_quality_sounds()
    elif choice == "3":
        print("👋 Nashledanou!")
    else:
        print("❌ Neplatná volba!")

if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Quality Sound Downloader
========================================

Stahuje kvalitní zvukové efekty z free zdrojů a upravuje je
pro použití v hře Light or Dead.

Zdroje:
- Mixkit.co (free, no attribution required)
- Freesound.org (Creative Commons)
- Zapsplat (free with registration)

Autor: AI Assistant
Verze: 1.0
"""

import requests
import os
import urllib.parse
from typing import List, Dict
import time

class QualitySoundDownloader:
    """Stahuje a upravuje kvalitní zvuky pro Light or Dead"""
    
    def __init__(self):
        self.output_dir = "game_assets/sounds/quality"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print("🎵 Quality Sound Downloader pro Light or Dead")
        print("=" * 60)
        print(f"📁 Výstupní složka: {self.output_dir}")
        print("🌐 Stahuji kvalitní zvuky z free zdrojů...")
    
    def download_file(self, url: str, filename: str) -> bool:
        """Stáhne soubor z URL"""
        try:
            print(f"⬇️ Stahuji: {filename}")
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, headers=headers, timeout=30)
            response.raise_for_status()
            
            filepath = os.path.join(self.output_dir, filename)
            with open(filepath, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ Staženo: {filename}")
            return True
            
        except Exception as e:
            print(f"❌ Chyba při stahování {filename}: {e}")
            return False
    
    def download_mixkit_sounds(self):
        """Stáhne zvuky z Mixkit (free, no attribution)"""
        print("\n🎮 Stahuji zvuky z Mixkit...")
        
        # Mixkit má přímé odkazy na WAV soubory
        mixkit_sounds = {
            # Zbraně a akce
            "mixkit_gun_shot.wav": "https://assets.mixkit.co/active_storage/sfx/2906/2906-preview.mp3",
            "mixkit_explosion.wav": "https://assets.mixkit.co/active_storage/sfx/1601/1601-preview.mp3", 
            "mixkit_hit.wav": "https://assets.mixkit.co/active_storage/sfx/2902/2902-preview.mp3",
            
            # UI zvuky
            "mixkit_button_click.wav": "https://assets.mixkit.co/active_storage/sfx/2997/2997-preview.mp3",
            "mixkit_pickup.wav": "https://assets.mixkit.co/active_storage/sfx/1003/1003-preview.mp3",
            "mixkit_powerup.wav": "https://assets.mixkit.co/active_storage/sfx/1004/1004-preview.mp3",
            "mixkit_error.wav": "https://assets.mixkit.co/active_storage/sfx/2955/2955-preview.mp3",
            
            # Hráč
            "mixkit_player_hurt.wav": "https://assets.mixkit.co/active_storage/sfx/2954/2954-preview.mp3",
            "mixkit_game_over.wav": "https://assets.mixkit.co/active_storage/sfx/2947/2947-preview.mp3",
        }
        
        for filename, url in mixkit_sounds.items():
            self.download_file(url, filename)
            time.sleep(1)  # Respektování serveru
    
    def create_sound_list(self):
        """Vytvoří seznam doporučených zvuků k manuálnímu stažení"""
        
        sound_recommendations = """
🎵 DOPORUČENÉ KVALITNÍ ZVUKY PRO LIGHT OR DEAD
=============================================

📥 MIXKIT.CO (Free, no attribution required):
   🔗 https://mixkit.co/free-sound-effects/game/

   Doporučené zvuky:
   🔫 Gun shots: "Martial arts fast punch", "Game blast"
   💥 Explosions: "Explosion", "Blast"
   🎯 Hits: "Fast punch", "Impact"
   🖱️ UI: "Button click", "Notification", "Success"
   🏃 Player: "Hurt", "Death", "Jump"

📥 FREESOUND.ORG (Creative Commons):
   🔗 https://freesound.org/

   Hledejte:
   - "gun shot" + tag:game
   - "explosion" + tag:8bit
   - "button click" + tag:ui
   - "hurt sound" + tag:game

📥 ZAPSPLAT.COM (Free with registration):
   🔗 https://www.zapsplat.com/
   
   Kategorie:
   - Game Audio > Weapons
   - Game Audio > UI
   - Game Audio > Character

🎯 NÁVOD:
1. Stáhněte zvuky do složky: game_assets/sounds/quality/
2. Přejmenujte je podle potřeby:
   - shoot.wav (pistol)
   - shotgun_shoot.wav
   - machinegun_shoot.wav
   - hit.wav
   - explosion.wav
   - enemy_death.wav
   - boss_spawn.wav
   - boss_death.wav
   - button_click.wav
   - shop_buy.wav
   - shop_error.wav
   - player_hurt.wav
   - game_over.wav
   - pickup.wav
   - powerup.wav
   - wave_start.wav
   - trap_trigger.wav

💡 TIPY:
- Hledejte krátké zvuky (0.1-2 sekundy)
- Preferujte WAV formát
- Kontrolujte licenci (CC0 nebo CC BY)
- Testujte zvuky před použitím
"""
        
        with open("quality_sounds_guide.txt", "w", encoding="utf-8") as f:
            f.write(sound_recommendations)
        
        print("📄 Vytvořen průvodce: quality_sounds_guide.txt")
    
    def create_sound_converter(self):
        """Vytvoří nástroj pro konverzi a úpravu stažených zvuků"""
        
        converter_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sound Converter pro Light or Dead
Konvertuje a upravuje stažené zvuky
"""

import os
import subprocess
import shutil

def convert_mp3_to_wav(input_dir="game_assets/sounds/quality"):
    """Konvertuje MP3 na WAV pomocí ffmpeg"""
    
    if not shutil.which("ffmpeg"):
        print("❌ FFmpeg není nainstalován!")
        print("💡 Stáhněte z: https://ffmpeg.org/download.html")
        return
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.mp3'):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(input_dir, filename.replace('.mp3', '.wav'))
            
            try:
                subprocess.run([
                    'ffmpeg', '-i', input_path, 
                    '-acodec', 'pcm_s16le', 
                    '-ar', '44100', 
                    output_path
                ], check=True, capture_output=True)
                
                print(f"✅ Konvertováno: {filename} → {filename.replace('.mp3', '.wav')}")
                os.remove(input_path)  # Smazání původního MP3
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Chyba konverze {filename}: {e}")

def normalize_volume(input_dir="game_assets/sounds/quality"):
    """Normalizuje hlasitost všech WAV souborů"""
    
    if not shutil.which("ffmpeg"):
        print("❌ FFmpeg není nainstalován!")
        return
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.wav'):
            input_path = os.path.join(input_dir, filename)
            temp_path = os.path.join(input_dir, f"temp_{filename}")
            
            try:
                # Normalizace na -3dB
                subprocess.run([
                    'ffmpeg', '-i', input_path,
                    '-filter:a', 'loudnorm=I=-16:TP=-1.5:LRA=11',
                    '-y', temp_path
                ], check=True, capture_output=True)
                
                os.replace(temp_path, input_path)
                print(f"🔊 Normalizováno: {filename}")
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Chyba normalizace {filename}: {e}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)

if __name__ == "__main__":
    print("🔧 Sound Converter pro Light or Dead")
    print("=" * 40)
    
    convert_mp3_to_wav()
    normalize_volume()
    
    print("\\n✅ Konverze dokončena!")
'''
        
        with open("sound_converter.py", "w", encoding="utf-8") as f:
            f.write(converter_code)
        
        print("🔧 Vytvořen nástroj: sound_converter.py")

def main():
    """Hlavní funkce"""
    
    downloader = QualitySoundDownloader()
    
    print("\n🎵 Co chcete udělat?")
    print("1. Stáhnout vzorové zvuky z Mixkit")
    print("2. Vytvořit průvodce pro manuální stahování")
    print("3. Vytvořit nástroj pro konverzi zvuků")
    print("4. Vše")
    
    choice = input("\nVyberte možnost (1-4): ").strip()
    
    if choice == "1" or choice == "4":
        downloader.download_mixkit_sounds()
    
    if choice == "2" or choice == "4":
        downloader.create_sound_list()
    
    if choice == "3" or choice == "4":
        downloader.create_sound_converter()
    
    print("\n✅ HOTOVO!")
    print("\n💡 Další kroky:")
    print("1. Přečtěte si quality_sounds_guide.txt")
    print("2. Stáhněte kvalitní zvuky z doporučených zdrojů")
    print("3. Spusťte sound_converter.py pro konverzi")
    print("4. Přejmenujte soubory podle herních názvů")

if __name__ == "__main__":
    main()

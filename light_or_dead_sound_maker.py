#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Advanced Sound Generator
========================================

Pokročilý generátor zvuků a hudby pro hru Light or Dead.
Vytváří atmosférické soundtracky a realistické zvukové efekty.

Autor: AI Assistant
Verze: 1.0
"""

import numpy as np
import scipy.signal as signal
import scipy.io.wavfile as wavfile
import matplotlib.pyplot as plt
import os
import random
import math
from typing import Tuple, List, Optional
import argparse

# Konstanty pro audio
SAMPLE_RATE = 44100  # Hz
BIT_DEPTH = 16
CHANNELS = 2  # Stereo

class SoundGenerator:
    """Gener<PERSON><PERSON> zvukových efektů a hudby pro Light or Dead"""
    
    def __init__(self, sample_rate: int = SAMPLE_RATE):
        self.sample_rate = sample_rate
        self.output_dir = "game_assets/sounds"
        
        # Vytvoření výstupní složky
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"🎵 Light or Dead Sound Generator inicializován")
        print(f"📁 Výstupní složka: {self.output_dir}")
        print(f"🔊 Sample rate: {self.sample_rate} Hz")
    
    def generate_tone(self, frequency: float, duration: float, 
                     amplitude: float = 0.5, fade_in: float = 0.0, 
                     fade_out: float = 0.0) -> np.ndarray:
        """Generuje základní tón s možností fade in/out"""
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Generování sinusového tónu
        wave = amplitude * np.sin(2 * np.pi * frequency * t)
        
        # Fade in
        if fade_in > 0:
            fade_samples = int(fade_in * self.sample_rate)
            fade_curve = np.linspace(0, 1, fade_samples)
            wave[:fade_samples] *= fade_curve
        
        # Fade out
        if fade_out > 0:
            fade_samples = int(fade_out * self.sample_rate)
            fade_curve = np.linspace(1, 0, fade_samples)
            wave[-fade_samples:] *= fade_curve
            
        return wave
    
    def add_noise(self, wave: np.ndarray, noise_level: float = 0.1) -> np.ndarray:
        """Přidá šum do zvuku pro realističtější efekt"""
        noise = np.random.normal(0, noise_level, len(wave))
        return wave + noise
    
    def apply_filter(self, wave: np.ndarray, filter_type: str,
                    cutoff, order: int = 5) -> np.ndarray:
        """Aplikuje filtr na zvuk"""
        nyquist = self.sample_rate / 2

        if filter_type == 'lowpass':
            normal_cutoff = cutoff / nyquist
            b, a = signal.butter(order, normal_cutoff, btype='low')
        elif filter_type == 'highpass':
            normal_cutoff = cutoff / nyquist
            b, a = signal.butter(order, normal_cutoff, btype='high')
        elif filter_type == 'bandpass':
            # Pro bandpass očekáváme list [low, high]
            if isinstance(cutoff, list) and len(cutoff) == 2:
                low, high = cutoff
                normal_cutoff = [low / nyquist, high / nyquist]
            else:
                return wave
            b, a = signal.butter(order, normal_cutoff, btype='band')
        else:
            return wave

        return signal.filtfilt(b, a, wave)
    
    def create_envelope(self, duration: float, attack: float = 0.1, 
                       decay: float = 0.1, sustain: float = 0.7, 
                       release: float = 0.2) -> np.ndarray:
        """Vytvoří ADSR envelope pro zvuk"""
        samples = int(duration * self.sample_rate)
        envelope = np.zeros(samples)
        
        # Attack
        attack_samples = int(attack * duration * self.sample_rate)
        envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
        
        # Decay
        decay_samples = int(decay * duration * self.sample_rate)
        decay_end = attack_samples + decay_samples
        envelope[attack_samples:decay_end] = np.linspace(1, sustain, decay_samples)
        
        # Sustain
        release_samples = int(release * duration * self.sample_rate)
        sustain_end = samples - release_samples
        envelope[decay_end:sustain_end] = sustain
        
        # Release
        envelope[sustain_end:] = np.linspace(sustain, 0, release_samples)
        
        return envelope
    
    def save_audio(self, audio_data: np.ndarray, filename: str, 
                  normalize: bool = True) -> None:
        """Uloží audio data do WAV souboru"""
        if normalize:
            # Normalizace na rozsah -1 až 1
            max_val = np.max(np.abs(audio_data))
            if max_val > 0:
                audio_data = audio_data / max_val
        
        # Konverze na 16-bit integer
        audio_int = (audio_data * 32767).astype(np.int16)
        
        filepath = os.path.join(self.output_dir, filename)
        wavfile.write(filepath, self.sample_rate, audio_int)
        print(f"✅ Uloženo: {filename}")
    
    def create_stereo(self, left: np.ndarray, right: Optional[np.ndarray] = None) -> np.ndarray:
        """Vytvoří stereo zvuk z mono kanálů"""
        if right is None:
            right = left.copy()
        
        # Zajistí stejnou délku
        min_len = min(len(left), len(right))
        left = left[:min_len]
        right = right[:min_len]
        
        return np.column_stack((left, right))

    # === ZVUKOVÉ EFEKTY PRO HRU ===
    
    def generate_weapon_sounds(self):
        """Generuje zvuky zbraní"""
        print("\n🔫 Generuji zvuky zbraní...")
        
        # Pistol - ostrý, krátký výstřel
        pistol_base = self.generate_tone(150, 0.1, 0.8)
        pistol_click = self.generate_tone(800, 0.05, 0.3)
        pistol = np.concatenate([pistol_base, pistol_click])
        pistol = self.add_noise(pistol, 0.2)
        pistol = self.apply_filter(pistol, 'highpass', 100)
        
        pistol_stereo = self.create_stereo(pistol)
        self.save_audio(pistol_stereo, "shoot.wav")
        
        # Shotgun - hlubší, delší výstřel
        shotgun_base = self.generate_tone(80, 0.2, 0.9)
        shotgun_boom = self.generate_tone(40, 0.2, 0.7)  # Stejná délka
        shotgun = shotgun_base + shotgun_boom
        shotgun = self.add_noise(shotgun, 0.3)
        
        shotgun_stereo = self.create_stereo(shotgun)
        self.save_audio(shotgun_stereo, "shotgun_shoot.wav")
        
        # Machine Gun - rychlý, ostrý
        mg_duration = 0.08
        mg_base = self.generate_tone(200, mg_duration, 0.6)
        mg_high = self.generate_tone(1200, mg_duration * 0.3, 0.4)
        mg = mg_base + np.concatenate([mg_high, np.zeros(len(mg_base) - len(mg_high))])
        mg = self.add_noise(mg, 0.15)
        
        mg_stereo = self.create_stereo(mg)
        self.save_audio(mg_stereo, "machinegun_shoot.wav")
    
    def generate_impact_sounds(self):
        """Generuje zvuky zásahů a explozí"""
        print("\n💥 Generuji zvuky zásahů...")
        
        # Hit - zásah nepřítele
        hit_freq = np.random.uniform(300, 600)
        hit = self.generate_tone(hit_freq, 0.15, 0.7, fade_out=0.1)
        hit = self.add_noise(hit, 0.2)
        hit = self.apply_filter(hit, 'bandpass', [200, 2000])
        
        hit_stereo = self.create_stereo(hit)
        self.save_audio(hit_stereo, "hit.wav")
        
        # Explosion - výbuch granátu/pasti
        explosion_low = self.generate_tone(60, 0.8, 0.9, fade_out=0.6)
        explosion_mid = self.generate_tone(200, 0.4, 0.6, fade_out=0.3)
        explosion_high = self.generate_tone(800, 0.2, 0.4, fade_out=0.15)
        
        # Kombinace frekvencí
        explosion = explosion_low.copy()
        explosion[:len(explosion_mid)] += explosion_mid
        explosion[:len(explosion_high)] += explosion_high
        explosion = self.add_noise(explosion, 0.4)
        
        explosion_stereo = self.create_stereo(explosion)
        self.save_audio(explosion_stereo, "explosion.wav")
    
    def generate_enemy_sounds(self):
        """Generuje zvuky nepřátel"""
        print("\n👹 Generuji zvuky nepřátel...")
        
        # Enemy death - smrt nepřítele
        death_freq = np.linspace(400, 100, int(0.5 * self.sample_rate))
        t = np.linspace(0, 0.5, len(death_freq))
        death = 0.6 * np.sin(2 * np.pi * death_freq * t)
        death = self.add_noise(death, 0.3)
        death = self.apply_filter(death, 'lowpass', 1000)
        
        death_stereo = self.create_stereo(death)
        self.save_audio(death_stereo, "enemy_death.wav")
        
        # Boss spawn - spawn bosse
        boss_spawn = np.zeros(int(2.0 * self.sample_rate))
        for i in range(5):
            freq = 100 + i * 50
            tone = self.generate_tone(freq, 0.3, 0.4 - i * 0.05)
            start_idx = int(i * 0.3 * self.sample_rate)
            end_idx = start_idx + len(tone)
            if end_idx <= len(boss_spawn):
                boss_spawn[start_idx:end_idx] += tone
        
        boss_spawn = self.add_noise(boss_spawn, 0.2)
        boss_spawn_stereo = self.create_stereo(boss_spawn)
        self.save_audio(boss_spawn_stereo, "boss_spawn.wav")
        
        # Boss death - smrt bosse
        boss_death = np.zeros(int(3.0 * self.sample_rate))
        for i in range(10):
            freq = 200 - i * 15
            tone = self.generate_tone(freq, 0.2, 0.5 - i * 0.03)
            start_idx = int(i * 0.25 * self.sample_rate)
            end_idx = start_idx + len(tone)
            if end_idx <= len(boss_death):
                boss_death[start_idx:end_idx] += tone
        
        boss_death = self.add_noise(boss_death, 0.3)
        boss_death_stereo = self.create_stereo(boss_death)
        self.save_audio(boss_death_stereo, "boss_death.wav")

    def generate_ui_sounds(self):
        """Generuje zvuky uživatelského rozhraní"""
        print("\n🖱️ Generuji UI zvuky...")

        # Button click - kliknutí na tlačítko
        click = self.generate_tone(800, 0.1, 0.5, fade_out=0.05)
        click = self.apply_filter(click, 'highpass', 400)
        click_stereo = self.create_stereo(click)
        self.save_audio(click_stereo, "button_click.wav")

        # Shop buy - úspěšný nákup
        buy_notes = [523, 659, 784]  # C, E, G
        buy_sound = np.zeros(int(0.6 * self.sample_rate))
        for i, freq in enumerate(buy_notes):
            note = self.generate_tone(freq, 0.15, 0.4, fade_in=0.02, fade_out=0.05)
            start_idx = int(i * 0.1 * self.sample_rate)
            end_idx = start_idx + len(note)
            if end_idx <= len(buy_sound):
                buy_sound[start_idx:end_idx] += note

        buy_stereo = self.create_stereo(buy_sound)
        self.save_audio(buy_stereo, "shop_buy.wav")

        # Error sound - chyba/nedostatek peněz
        error = self.generate_tone(200, 0.3, 0.6, fade_out=0.2)
        error = self.add_noise(error, 0.1)
        error = self.apply_filter(error, 'lowpass', 500)
        error_stereo = self.create_stereo(error)
        self.save_audio(error_stereo, "shop_error.wav")

        # Wave start - začátek vlny
        wave_start = np.zeros(int(1.5 * self.sample_rate))
        for i in range(8):
            freq = 220 + i * 55  # A major scale
            tone = self.generate_tone(freq, 0.15, 0.3 - i * 0.02)
            start_idx = int(i * 0.15 * self.sample_rate)
            end_idx = start_idx + len(tone)
            if end_idx <= len(wave_start):
                wave_start[start_idx:end_idx] += tone

        wave_stereo = self.create_stereo(wave_start)
        self.save_audio(wave_stereo, "wave_start.wav")

    def generate_player_sounds(self):
        """Generuje zvuky hráče"""
        print("\n🏃 Generuji zvuky hráče...")

        # Player hurt - zranění hráče
        hurt_freq = np.linspace(600, 200, int(0.4 * self.sample_rate))
        t = np.linspace(0, 0.4, len(hurt_freq))
        hurt = 0.7 * np.sin(2 * np.pi * hurt_freq * t)
        hurt = self.add_noise(hurt, 0.2)
        hurt_stereo = self.create_stereo(hurt)
        self.save_audio(hurt_stereo, "player_hurt.wav")

        # Game over - konec hry
        game_over = np.zeros(int(2.5 * self.sample_rate))
        # Sestupná sekvence tónů
        frequencies = [440, 392, 349, 311, 277, 247, 220]  # A minor scale down
        for i, freq in enumerate(frequencies):
            tone = self.generate_tone(freq, 0.3, 0.5 - i * 0.05, fade_out=0.1)
            start_idx = int(i * 0.25 * self.sample_rate)
            end_idx = start_idx + len(tone)
            if end_idx <= len(game_over):
                game_over[start_idx:end_idx] += tone

        game_over_stereo = self.create_stereo(game_over)
        self.save_audio(game_over_stereo, "game_over.wav")

        # Pickup - sebírání předmětů
        pickup_notes = [440, 554, 659]  # A, C#, E
        pickup = np.zeros(int(0.4 * self.sample_rate))
        for i, freq in enumerate(pickup_notes):
            note = self.generate_tone(freq, 0.1, 0.4, fade_in=0.01, fade_out=0.03)
            start_idx = int(i * 0.08 * self.sample_rate)
            end_idx = start_idx + len(note)
            if end_idx <= len(pickup):
                pickup[start_idx:end_idx] += note

        pickup_stereo = self.create_stereo(pickup)
        self.save_audio(pickup_stereo, "pickup.wav")

        # Powerup - získání vylepšení
        powerup = np.zeros(int(1.0 * self.sample_rate))
        powerup_freqs = [220, 277, 330, 440, 554, 659, 880]  # Vzestupná sekvence
        for i, freq in enumerate(powerup_freqs):
            tone = self.generate_tone(freq, 0.12, 0.4, fade_in=0.02, fade_out=0.04)
            start_idx = int(i * 0.1 * self.sample_rate)
            end_idx = start_idx + len(tone)
            if end_idx <= len(powerup):
                powerup[start_idx:end_idx] += tone

        powerup_stereo = self.create_stereo(powerup)
        self.save_audio(powerup_stereo, "powerup.wav")

    def generate_trap_sounds(self):
        """Generuje zvuky pastí"""
        print("\n🪤 Generuji zvuky pastí...")

        # Trap trigger - aktivace pasti
        trap_base = self.generate_tone(300, 0.2, 0.6)
        trap_click = self.generate_tone(1200, 0.05, 0.4)
        trap = np.concatenate([trap_click, trap_base])
        trap = self.add_noise(trap, 0.2)
        trap_stereo = self.create_stereo(trap)
        self.save_audio(trap_stereo, "trap_trigger.wav")

class MusicGenerator:
    """Generátor hudby pro Light or Dead"""

    def __init__(self, sample_rate: int = SAMPLE_RATE):
        self.sample_rate = sample_rate
        self.output_dir = "game_assets/sounds"
        os.makedirs(self.output_dir, exist_ok=True)

        # Hudební konstanty
        self.bpm = 80  # Pomalé, atmosférické tempo
        self.beat_duration = 60.0 / self.bpm

        # Tóniny pro různé nálady
        self.minor_scale = [220, 246, 261, 293, 329, 349, 391]  # A minor
        self.diminished_scale = [220, 246, 277, 311, 349, 391, 415]  # Více temné

    def generate_chord(self, root_freq: float, chord_type: str,
                      duration: float, amplitude: float = 0.3) -> np.ndarray:
        """Generuje akord"""
        if chord_type == 'minor':
            intervals = [1, 1.2, 1.5]  # Minor triad
        elif chord_type == 'diminished':
            intervals = [1, 1.2, 1.414]  # Diminished triad
        elif chord_type == 'major':
            intervals = [1, 1.25, 1.5]  # Major triad
        else:
            intervals = [1]

        chord = np.zeros(int(duration * self.sample_rate))
        for interval in intervals:
            freq = root_freq * interval
            tone = self.generate_tone(freq, duration, amplitude / len(intervals))
            chord += tone

        return chord

    def generate_tone(self, frequency: float, duration: float,
                     amplitude: float = 0.5) -> np.ndarray:
        """Generuje tón s ADSR envelope"""
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)

        # Základní sinusová vlna
        wave = amplitude * np.sin(2 * np.pi * frequency * t)

        # ADSR envelope pro realistický zvuk
        attack_time = min(0.1, duration * 0.1)
        decay_time = min(0.1, duration * 0.1)
        sustain_level = 0.7
        release_time = min(0.2, duration * 0.3)

        attack_samples = int(attack_time * self.sample_rate)
        decay_samples = int(decay_time * self.sample_rate)
        release_samples = int(release_time * self.sample_rate)
        sustain_samples = samples - attack_samples - decay_samples - release_samples

        envelope = np.ones(samples)

        # Attack
        if attack_samples > 0:
            envelope[:attack_samples] = np.linspace(0, 1, attack_samples)

        # Decay
        if decay_samples > 0:
            start_idx = attack_samples
            end_idx = start_idx + decay_samples
            envelope[start_idx:end_idx] = np.linspace(1, sustain_level, decay_samples)

        # Sustain
        if sustain_samples > 0:
            start_idx = attack_samples + decay_samples
            end_idx = start_idx + sustain_samples
            envelope[start_idx:end_idx] = sustain_level

        # Release
        if release_samples > 0:
            envelope[-release_samples:] = np.linspace(sustain_level, 0, release_samples)

        return wave * envelope

    def add_reverb(self, audio: np.ndarray, room_size: float = 0.3,
                   damping: float = 0.5) -> np.ndarray:
        """Přidá reverb efekt pro atmosféru"""
        delay_samples = int(0.05 * self.sample_rate)  # 50ms delay
        reverb = np.zeros(len(audio) + delay_samples)
        reverb[:len(audio)] = audio

        # Několik echo efektů s klesající amplitudou
        for i in range(1, 6):
            delay = int(i * 0.03 * self.sample_rate)
            amplitude = (0.6 ** i) * room_size
            if delay < len(reverb) - len(audio):
                reverb[delay:delay + len(audio)] += audio * amplitude

        return reverb[:len(audio)]

    def save_audio(self, audio_data: np.ndarray, filename: str) -> None:
        """Uloží audio data do WAV souboru"""
        # Normalizace
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            audio_data = audio_data / max_val

        # Konverze na 16-bit integer
        audio_int = (audio_data * 32767).astype(np.int16)

        filepath = os.path.join(self.output_dir, filename)
        wavfile.write(filepath, self.sample_rate, audio_int)
        print(f"🎵 Uloženo: {filename}")

    def create_stereo(self, left: np.ndarray, right: Optional[np.ndarray] = None) -> np.ndarray:
        """Vytvoří stereo zvuk"""
        if right is None:
            right = left.copy()

        min_len = min(len(left), len(right))
        left = left[:min_len]
        right = right[:min_len]

        return np.column_stack((left, right))

    def generate_ambient_background(self, duration: float = 60.0) -> None:
        """Generuje ambientní pozadí pro menu a klidné chvíle"""
        print(f"\n🌙 Generuji ambientní hudbu ({duration}s)...")

        total_samples = int(duration * self.sample_rate)
        background = np.zeros(total_samples)

        # Základní drone tóny
        drone_freqs = [55, 82.4, 110]  # A1, E2, A2
        for freq in drone_freqs:
            drone = self.generate_tone(freq, duration, 0.15)
            background += drone

        # Pomalu se měnící harmonické
        num_sections = 8
        section_duration = duration / num_sections

        for section in range(num_sections):
            start_time = section * section_duration
            start_sample = int(start_time * self.sample_rate)

            # Náhodné harmonické tóny z minor scale
            harmony_freq = random.choice(self.minor_scale) * 2
            harmony = self.generate_tone(harmony_freq, section_duration, 0.08)

            end_sample = start_sample + len(harmony)
            if end_sample <= len(background):
                background[start_sample:end_sample] += harmony

        # Přidání reverbu pro atmosféru
        background = self.add_reverb(background, room_size=0.4)

        # Stereo verze s mírným rozdílem pro prostorový efekt
        left = background
        right = background * 0.95  # Mírně tišší pravý kanál
        stereo_bg = self.create_stereo(left, right)

        self.save_audio(stereo_bg, "ambient_background.wav")

    def generate_action_music(self, duration: float = 45.0) -> None:
        """Generuje akční hudbu pro boj"""
        print(f"\n⚔️ Generuji akční hudbu ({duration}s)...")

        total_samples = int(duration * self.sample_rate)
        action_music = np.zeros(total_samples)

        # Rychlejší tempo pro akci
        action_bpm = 120
        beat_duration = 60.0 / action_bpm

        # Základní rytmus - basová linka
        bass_pattern = [0, 0, 1, 0, 0, 1, 0, 1]  # Rytmický pattern
        bass_freq = 55  # A1

        beat_samples = int(beat_duration * self.sample_rate)
        for beat in range(int(duration / beat_duration)):
            if beat < len(bass_pattern) * 10:  # Opakování patternu
                pattern_index = beat % len(bass_pattern)
                if bass_pattern[pattern_index]:
                    start_sample = beat * beat_samples
                    bass_note = self.generate_tone(bass_freq, beat_duration * 0.8, 0.3)

                    end_sample = start_sample + len(bass_note)
                    if end_sample <= len(action_music):
                        action_music[start_sample:end_sample] += bass_note

        # Melodické prvky - napětí budující sekvence
        melody_times = np.linspace(0, duration, 16)
        melody_freqs = [220, 246, 277, 293, 329, 349, 391, 440]  # Vzestupná sekvence

        for i, (time, freq) in enumerate(zip(melody_times, melody_freqs * 2)):
            start_sample = int(time * self.sample_rate)
            note_duration = beat_duration * 2
            melody_note = self.generate_tone(freq, note_duration, 0.2)

            end_sample = start_sample + len(melody_note)
            if end_sample <= len(action_music):
                action_music[start_sample:end_sample] += melody_note

        # Přidání reverbu
        action_music = self.add_reverb(action_music, room_size=0.2)

        # Stereo mix
        stereo_action = self.create_stereo(action_music)
        self.save_audio(stereo_action, "action_music.wav")

    def generate_boss_music(self, duration: float = 90.0) -> None:
        """Generuje epickou hudbu pro boss fightu"""
        print(f"\n👹 Generuji boss hudbu ({duration}s)...")

        total_samples = int(duration * self.sample_rate)
        boss_music = np.zeros(total_samples)

        # Pomalé, hrozivé tempo
        boss_bpm = 60
        beat_duration = 60.0 / boss_bpm

        # Hluboké, hrozivé tóny
        bass_freqs = [41.2, 55, 73.4]  # E1, A1, D2

        # Vytvoření dramatických akordů
        chord_progression = [
            (55, 'diminished'),   # A diminished
            (61.7, 'minor'),      # B minor
            (73.4, 'diminished'), # D diminished
            (82.4, 'minor')       # E minor
        ]

        beats_per_chord = 8
        for chord_idx, (root_freq, chord_type) in enumerate(chord_progression * 3):
            start_beat = chord_idx * beats_per_chord
            start_time = start_beat * beat_duration
            start_sample = int(start_time * self.sample_rate)

            chord_duration = beats_per_chord * beat_duration
            chord = self.generate_chord(root_freq, chord_type, chord_duration, 0.25)

            end_sample = start_sample + len(chord)
            if end_sample <= len(boss_music):
                boss_music[start_sample:end_sample] += chord

        # Dramatické melodické prvky
        dramatic_times = np.linspace(0, duration, 32)
        for i, time in enumerate(dramatic_times):
            if i % 4 == 0:  # Každý čtvrtý beat
                start_sample = int(time * self.sample_rate)
                freq = self.diminished_scale[i % len(self.diminished_scale)] * 4
                dramatic_note = self.generate_tone(freq, beat_duration, 0.15)

                end_sample = start_sample + len(dramatic_note)
                if end_sample <= len(boss_music):
                    boss_music[start_sample:end_sample] += dramatic_note

        # Silný reverb pro epický efekt
        boss_music = self.add_reverb(boss_music, room_size=0.6)

        # Stereo mix s širším stereo obrazem
        left = boss_music * 1.1
        right = boss_music * 0.9
        stereo_boss = self.create_stereo(left, right)

        self.save_audio(stereo_boss, "boss_music.wav")

def main():
    """Hlavní funkce pro generování všech zvuků"""
    print("🎮 Light or Dead - Sound Generator")
    print("=" * 50)

    # Vytvoření generátorů
    sound_gen = SoundGenerator()
    music_gen = MusicGenerator()

    # Generování zvukových efektů
    print("\n🔊 GENEROVÁNÍ ZVUKOVÝCH EFEKTŮ")
    print("-" * 30)

    sound_gen.generate_weapon_sounds()
    sound_gen.generate_impact_sounds()
    sound_gen.generate_enemy_sounds()
    sound_gen.generate_ui_sounds()
    sound_gen.generate_player_sounds()
    sound_gen.generate_trap_sounds()

    # Generování hudby
    print("\n🎵 GENEROVÁNÍ HUDBY")
    print("-" * 20)

    music_gen.generate_ambient_background(duration=120)  # 2 minuty ambientu
    music_gen.generate_action_music(duration=60)         # 1 minuta akce
    music_gen.generate_boss_music(duration=180)          # 3 minuty boss hudby

    print("\n✅ HOTOVO!")
    print(f"📁 Všechny soubory uloženy do: {sound_gen.output_dir}")
    print("\n🎮 Můžete nyní spustit hru a vychutnat si nové zvuky!")

if __name__ == "__main__":
    # Kontrola závislostí
    try:
        import numpy as np
        import scipy.signal
        import scipy.io.wavfile
        print("✅ Všechny závislosti jsou k dispozici")
    except ImportError as e:
        print(f"❌ Chybí závislost: {e}")
        print("💡 Nainstalujte pomocí: pip install numpy scipy matplotlib")
        exit(1)

    main()

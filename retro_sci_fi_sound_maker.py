#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Light or Dead - Retro Sci-Fi Sound Effects Generator
===================================================

Specializovaný generátor pro retro sci-fi zvukové efekty
používající klasické techniky syntézy zvuku.

Autor: AI Assistant
Verze: 1.0
"""

import numpy as np
import scipy.signal as signal
import scipy.io.wavfile as wavfile
import os
import random
import math
from typing import Tuple, Optional

class RetroSciFireSoundGenerator:
    """Generátor retro sci-fi zvukových efektů"""
    
    def __init__(self, sample_rate: int = 44100):
        self.sample_rate = sample_rate
        self.output_dir = "game_assets/sounds/retro_effects"
        os.makedirs(self.output_dir, exist_ok=True)
        
        print(f"🚀 Retro Sci-Fi Sound Generator inicializován")
        print(f"📁 Výstupní složka: {self.output_dir}")
    
    def generate_white_noise(self, duration: float, amplitude: float = 0.5) -> np.ndarray:
        """Generuje bílý šum"""
        samples = int(duration * self.sample_rate)
        return amplitude * np.random.normal(0, 1, samples)
    
    def generate_square_wave(self, frequency: float, duration: float, 
                           amplitude: float = 0.5, duty_cycle: float = 0.5) -> np.ndarray:
        """Generuje čtvercovou vlnu"""
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Čtvercová vlna pomocí sign funkce
        sine_wave = np.sin(2 * np.pi * frequency * t)
        square_wave = amplitude * np.sign(sine_wave)
        
        # Aplikace duty cycle
        if duty_cycle != 0.5:
            period_samples = int(self.sample_rate / frequency)
            for i in range(0, samples, period_samples):
                high_samples = int(period_samples * duty_cycle)
                if i + high_samples < samples:
                    square_wave[i:i + high_samples] = amplitude
                if i + period_samples < samples:
                    square_wave[i + high_samples:i + period_samples] = -amplitude
        
        return square_wave
    
    def apply_envelope(self, audio: np.ndarray, attack: float = 0.1, 
                      decay: float = 0.1, sustain: float = 0.7, 
                      release: float = 0.2) -> np.ndarray:
        """Aplikuje ADSR envelope na audio signál"""
        samples = len(audio)
        duration = samples / self.sample_rate
        
        attack_samples = int(attack * duration * self.sample_rate)
        decay_samples = int(decay * duration * self.sample_rate)
        release_samples = int(release * duration * self.sample_rate)
        sustain_samples = samples - attack_samples - decay_samples - release_samples
        
        envelope = np.ones(samples)
        
        # Attack
        if attack_samples > 0:
            envelope[:attack_samples] = np.linspace(0, 1, attack_samples)
        
        # Decay
        if decay_samples > 0:
            start_idx = attack_samples
            end_idx = start_idx + decay_samples
            envelope[start_idx:end_idx] = np.linspace(1, sustain, decay_samples)
        
        # Sustain
        if sustain_samples > 0:
            start_idx = attack_samples + decay_samples
            end_idx = start_idx + sustain_samples
            envelope[start_idx:end_idx] = sustain
        
        # Release
        if release_samples > 0:
            envelope[-release_samples:] = np.linspace(sustain, 0, release_samples)
        
        return audio * envelope
    
    def apply_pitch_drop(self, audio: np.ndarray, start_pitch: float = 1.0, 
                        end_pitch: float = 0.3) -> np.ndarray:
        """Aplikuje pitch drop efekt (změna rychlosti přehrávání)"""
        samples = len(audio)
        
        # Vytvoření pitch curve
        pitch_curve = np.linspace(start_pitch, end_pitch, samples)
        
        # Resample podle pitch curve
        output_samples = int(samples / np.mean(pitch_curve))
        output = np.zeros(output_samples)
        
        current_pos = 0.0
        for i in range(output_samples):
            if int(current_pos) < samples - 1:
                # Lineární interpolace
                idx = int(current_pos)
                frac = current_pos - idx
                output[i] = audio[idx] * (1 - frac) + audio[idx + 1] * frac
            
            # Aktualizace pozice podle aktuálního pitch
            pitch_idx = min(int(i * samples / output_samples), samples - 1)
            current_pos += pitch_curve[pitch_idx]
        
        return output
    
    def apply_lowpass_filter(self, audio: np.ndarray, cutoff_freq: float, 
                           order: int = 5) -> np.ndarray:
        """Aplikuje dolní propust"""
        nyquist = self.sample_rate / 2
        normal_cutoff = cutoff_freq / nyquist
        b, a = signal.butter(order, normal_cutoff, btype='low')
        return signal.filtfilt(b, a, audio)
    
    def apply_filter_sweep(self, audio: np.ndarray, start_freq: float, 
                          end_freq: float) -> np.ndarray:
        """Aplikuje sweep filter efekt"""
        samples = len(audio)
        output = np.zeros(samples)
        
        # Rozdělení na malé segmenty pro postupnou změnu filtru
        segment_size = 1024
        num_segments = samples // segment_size
        
        for i in range(num_segments):
            start_idx = i * segment_size
            end_idx = min(start_idx + segment_size, samples)
            
            # Aktuální frekvence filtru
            progress = i / max(num_segments - 1, 1)
            current_freq = start_freq + (end_freq - start_freq) * progress
            
            # Aplikace filtru na segment
            segment = audio[start_idx:end_idx]
            filtered_segment = self.apply_lowpass_filter(segment, current_freq)
            output[start_idx:end_idx] = filtered_segment
        
        return output
    
    def create_laser_shot(self) -> np.ndarray:
        """White Noise + Envelope → výstřel nebo exploze"""
        print("🔫 Generuji laser výstřel...")
        
        duration = 0.3
        
        # Bílý šum jako základ
        noise = self.generate_white_noise(duration, 0.8)
        
        # Aplikace envelope pro výstřel
        laser = self.apply_envelope(noise, attack=0.01, decay=0.05, 
                                  sustain=0.3, release=0.24)
        
        # Vysokofrekvenční filtr pro "laser" zvuk
        laser = self.apply_lowpass_filter(laser, 8000)
        
        return laser
    
    def create_retro_laser(self) -> np.ndarray:
        """Square wave + pitch drop → laser nebo retro výstřel"""
        print("⚡ Generuji retro laser...")
        
        duration = 0.4
        start_freq = 800
        
        # Čtvercová vlna
        square = self.generate_square_wave(start_freq, duration, 0.6)
        
        # Pitch drop efekt
        retro_laser = self.apply_pitch_drop(square, start_pitch=1.0, end_pitch=0.2)
        
        # Envelope pro ostřejší začátek
        retro_laser = self.apply_envelope(retro_laser, attack=0.01, decay=0.1, 
                                        sustain=0.6, release=0.29)
        
        return retro_laser
    
    def create_button_click(self) -> np.ndarray:
        """Short sine + attack envelope → kliknutí"""
        print("🖱️ Generuji retro kliknutí...")
        
        duration = 0.1
        frequency = 1200
        
        # Krátký sinusový tón
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        sine = 0.5 * np.sin(2 * np.pi * frequency * t)
        
        # Rychlý attack envelope
        click = self.apply_envelope(sine, attack=0.01, decay=0.02, 
                                  sustain=0.3, release=0.07)
        
        return click
    
    def create_explosion(self) -> np.ndarray:
        """Noise + LP filter sweep → výbuch nebo zásah"""
        print("💥 Generuji sci-fi výbuch...")
        
        duration = 0.8
        
        # Bílý šum jako základ
        noise = self.generate_white_noise(duration, 0.9)
        
        # Filter sweep od vysokých k nízkým frekvencím
        explosion = self.apply_filter_sweep(noise, start_freq=8000, end_freq=200)
        
        # Envelope pro výbuch
        explosion = self.apply_envelope(explosion, attack=0.01, decay=0.2, 
                                      sustain=0.4, release=0.39)
        
        return explosion
    
    def create_power_up(self) -> np.ndarray:
        """Vzestupná sekvence tónů pro power-up"""
        print("⭐ Generuji power-up zvuk...")
        
        # Sekvence frekvencí (pentatonická stupnice)
        frequencies = [220, 277, 330, 440, 554]  # A, C#, E, A, C#
        note_duration = 0.1
        total_duration = len(frequencies) * note_duration
        
        samples = int(total_duration * self.sample_rate)
        powerup = np.zeros(samples)
        
        for i, freq in enumerate(frequencies):
            start_sample = int(i * note_duration * self.sample_rate)
            
            # Čtvercová vlna pro retro zvuk
            note = self.generate_square_wave(freq, note_duration, 0.4)
            note = self.apply_envelope(note, attack=0.01, decay=0.02, 
                                     sustain=0.7, release=0.07)
            
            end_sample = start_sample + len(note)
            if end_sample <= len(powerup):
                powerup[start_sample:end_sample] += note
        
        return powerup
    
    def create_enemy_hit(self) -> np.ndarray:
        """Kombinace šumu a tónu pro zásah nepřítele"""
        print("👾 Generuji zásah nepřítele...")
        
        duration = 0.2
        
        # Kombinace šumu a nízkého tónu
        noise = self.generate_white_noise(duration, 0.4)
        tone = self.generate_square_wave(150, duration, 0.3)
        
        hit = noise + tone
        
        # Rychlý útlum
        hit = self.apply_envelope(hit, attack=0.01, decay=0.05, 
                                sustain=0.2, release=0.14)
        
        # Filtr pro tlumení vysokých frekvencí
        hit = self.apply_lowpass_filter(hit, 2000)
        
        return hit
    
    def create_teleport(self) -> np.ndarray:
        """Efekt teleportace s modulací"""
        print("🌀 Generuji teleport efekt...")
        
        duration = 1.0
        base_freq = 400
        
        samples = int(duration * self.sample_rate)
        t = np.linspace(0, duration, samples, False)
        
        # Modulovaný tón
        modulation = 20 * np.sin(2 * np.pi * 8 * t)  # 8 Hz modulace
        frequency_curve = base_freq + modulation
        
        # Generování modulovaného signálu
        phase = np.cumsum(2 * np.pi * frequency_curve / self.sample_rate)
        teleport = 0.5 * np.sin(phase)
        
        # Fade in a fade out
        teleport = self.apply_envelope(teleport, attack=0.3, decay=0.1, 
                                     sustain=0.6, release=0.3)
        
        # Reverb efekt
        teleport = self.add_reverb(teleport, 0.4)
        
        return teleport
    
    def add_reverb(self, audio: np.ndarray, room_size: float = 0.3) -> np.ndarray:
        """Přidá reverb efekt"""
        delay_samples = int(0.05 * self.sample_rate)
        reverb = np.zeros(len(audio) + delay_samples)
        reverb[:len(audio)] = audio
        
        for i in range(1, 6):
            delay = int(i * 0.03 * self.sample_rate)
            amplitude = (0.6 ** i) * room_size
            if delay < len(reverb) - len(audio):
                reverb[delay:delay + len(audio)] += audio * amplitude
        
        return reverb[:len(audio)]
    
    def save_audio(self, audio_data: np.ndarray, filename: str) -> None:
        """Uloží audio data do WAV souboru"""
        # Normalizace
        max_val = np.max(np.abs(audio_data))
        if max_val > 0:
            audio_data = audio_data / max_val
        
        # Stereo
        stereo_data = np.column_stack((audio_data, audio_data))
        
        # Konverze na 16-bit
        audio_int = (stereo_data * 32767).astype(np.int16)
        
        filepath = os.path.join(self.output_dir, filename)
        wavfile.write(filepath, self.sample_rate, audio_int)
        print(f"✅ Uloženo: {filename}")
    
    def generate_all_retro_effects(self):
        """Generuje všechny retro sci-fi efekty"""
        print("\n🚀 GENEROVÁNÍ RETRO SCI-FI EFEKTŮ")
        print("-" * 40)
        
        # Laser efekty
        laser_shot = self.create_laser_shot()
        self.save_audio(laser_shot, "laser_shot.wav")
        
        retro_laser = self.create_retro_laser()
        self.save_audio(retro_laser, "retro_laser.wav")
        
        # UI zvuky
        button_click = self.create_button_click()
        self.save_audio(button_click, "retro_button_click.wav")
        
        # Výbuchy a zásahy
        explosion = self.create_explosion()
        self.save_audio(explosion, "sci_fi_explosion.wav")
        
        enemy_hit = self.create_enemy_hit()
        self.save_audio(enemy_hit, "enemy_hit_retro.wav")
        
        # Speciální efekty
        power_up = self.create_power_up()
        self.save_audio(power_up, "retro_power_up.wav")
        
        teleport = self.create_teleport()
        self.save_audio(teleport, "teleport_effect.wav")

def main():
    """Hlavní funkce"""
    print("🚀 Light or Dead - Retro Sci-Fi Sound Effects Generator")
    print("=" * 60)
    
    generator = RetroSciFireSoundGenerator()
    generator.generate_all_retro_effects()
    
    print("\n✅ VŠECHNY RETRO EFEKTY VYGENEROVÁNY!")
    print(f"📁 Soubory uloženy do: {generator.output_dir}")
    print("\n🎮 Retro sci-fi zvuky jsou připraveny pro vaši hru!")
    print("\n💡 Použité techniky:")
    print("   • White Noise + Envelope → laser výstřely")
    print("   • Square wave + pitch drop → retro lasery") 
    print("   • Short sine + attack envelope → kliknutí")
    print("   • Noise + LP filter sweep → výbuchy")

if __name__ == "__main__":
    main()

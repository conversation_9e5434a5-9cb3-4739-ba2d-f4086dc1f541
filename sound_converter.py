#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Sound Converter pro Light or Dead
Konvertuje a upravuje stažené z<PERSON>
"""

import os
import subprocess
import shutil

def convert_mp3_to_wav(input_dir="game_assets/sounds/quality"):
    """Konvertuje MP3 na WAV pomocí ffmpeg"""
    
    if not shutil.which("ffmpeg"):
        print("❌ FFmpeg není nainstalován!")
        print("💡 Stáhněte z: https://ffmpeg.org/download.html")
        return
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.mp3'):
            input_path = os.path.join(input_dir, filename)
            output_path = os.path.join(input_dir, filename.replace('.mp3', '.wav'))
            
            try:
                subprocess.run([
                    'ffmpeg', '-i', input_path, 
                    '-acodec', 'pcm_s16le', 
                    '-ar', '44100', 
                    output_path
                ], check=True, capture_output=True)
                
                print(f"✅ Konvertováno: {filename} → {filename.replace('.mp3', '.wav')}")
                os.remove(input_path)  # Smazání původního MP3
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Chyba konverze {filename}: {e}")

def normalize_volume(input_dir="game_assets/sounds/quality"):
    """Normalizuje hlasitost všech WAV souborů"""
    
    if not shutil.which("ffmpeg"):
        print("❌ FFmpeg není nainstalován!")
        return
    
    for filename in os.listdir(input_dir):
        if filename.endswith('.wav'):
            input_path = os.path.join(input_dir, filename)
            temp_path = os.path.join(input_dir, f"temp_{filename}")
            
            try:
                # Normalizace na -3dB
                subprocess.run([
                    'ffmpeg', '-i', input_path,
                    '-filter:a', 'loudnorm=I=-16:TP=-1.5:LRA=11',
                    '-y', temp_path
                ], check=True, capture_output=True)
                
                os.replace(temp_path, input_path)
                print(f"🔊 Normalizováno: {filename}")
                
            except subprocess.CalledProcessError as e:
                print(f"❌ Chyba normalizace {filename}: {e}")
                if os.path.exists(temp_path):
                    os.remove(temp_path)

if __name__ == "__main__":
    print("🔧 Sound Converter pro Light or Dead")
    print("=" * 40)
    
    convert_mp3_to_wav()
    normalize_volume()
    
    print("\n✅ Konverze dokončena!")
